/**
 * DmrThema Customizer Preview JavaScript
 * Renk değişikliklerini canlı olarak önizleme
 */

(function($) {
    'use strict';

    // Ana renk değiştiğinde
    wp.customize('dmrthema_primary_color', function(value) {
        value.bind(function(newval) {
            // CSS değişkenlerini güncelle
            document.documentElement.style.setProperty('--dmrthema-primary-color', newval);
            
            // Diğer renkleri otomatik hesapla ve güncelle
            var secondaryColor = lightenColor(newval, 20);
            var accentColor = darkenColor(newval, 20);
            var lightColor = lightenColor(newval, 85);
            
            document.documentElement.style.setProperty('--dmrthema-secondary-color', secondaryColor);
            document.documentElement.style.setProperty('--dmrthema-accent-color', accentColor);
            document.documentElement.style.setProperty('--dmrthema-light-color', lightColor);
        });
    });

    // İkincil renk değiştiğinde
    wp.customize('dmrthema_secondary_color', function(value) {
        value.bind(function(newval) {
            document.documentElement.style.setProperty('--dmrthema-secondary-color', newval);
        });
    });

    // Vurgu rengi değiştiğinde
    wp.customize('dmrthema_accent_color', function(value) {
        value.bind(function(newval) {
            document.documentElement.style.setProperty('--dmrthema-accent-color', newval);
        });
    });

    // Açık renk değiştiğinde
    wp.customize('dmrthema_light_color', function(value) {
        value.bind(function(newval) {
            document.documentElement.style.setProperty('--dmrthema-light-color', newval);
        });
    });

    // Rengi açıklaştırma fonksiyonu
    function lightenColor(hex, percent) {
        hex = hex.replace('#', '');
        
        if (hex.length === 3) {
            hex = hex[0] + hex[0] + hex[1] + hex[1] + hex[2] + hex[2];
        }
        
        var r = parseInt(hex.substr(0, 2), 16);
        var g = parseInt(hex.substr(2, 2), 16);
        var b = parseInt(hex.substr(4, 2), 16);
        
        r = Math.min(255, r + (255 - r) * percent / 100);
        g = Math.min(255, g + (255 - g) * percent / 100);
        b = Math.min(255, b + (255 - b) * percent / 100);
        
        return '#' + Math.round(r).toString(16).padStart(2, '0') + 
                   Math.round(g).toString(16).padStart(2, '0') + 
                   Math.round(b).toString(16).padStart(2, '0');
    }

    // Rengi koyulaştırma fonksiyonu
    function darkenColor(hex, percent) {
        hex = hex.replace('#', '');
        
        if (hex.length === 3) {
            hex = hex[0] + hex[0] + hex[1] + hex[1] + hex[2] + hex[2];
        }
        
        var r = parseInt(hex.substr(0, 2), 16);
        var g = parseInt(hex.substr(2, 2), 16);
        var b = parseInt(hex.substr(4, 2), 16);
        
        r = Math.max(0, r - r * percent / 100);
        g = Math.max(0, g - g * percent / 100);
        b = Math.max(0, b - b * percent / 100);
        
        return '#' + Math.round(r).toString(16).padStart(2, '0') + 
                   Math.round(g).toString(16).padStart(2, '0') + 
                   Math.round(b).toString(16).padStart(2, '0');
    }

})(jQuery); 