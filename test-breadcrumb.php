<?php
/**
 * Test Breadcrumb Sayfasi
 * Breadcrumb'in calisip calismadigi test edilir
 */

// WordPress'i yukle
require_once('../../../wp-load.php');

// WooCommerce aktif mi kontrol et
if (!class_exists('WooCommerce')) {
    die('WooCommerce aktif degil!');
}

// Breadcrumb fonksiyonunu test et
echo '<h2>Breadcrumb Test</h2>';

// Urun sayfasi simule et
global $post;
$products = get_posts(array(
    'post_type' => 'product',
    'posts_per_page' => 1,
    'post_status' => 'publish'
));

if (!empty($products)) {
    $post = $products[0];
    setup_postdata($post);
    
    echo '<h3>Urun: ' . get_the_title() . '</h3>';
    
    // Breadcrumb'i goster
    if (function_exists('woocommerce_breadcrumb')) {
        echo '<div style="border: 2px solid red; padding: 10px; margin: 10px 0;">';
        echo '<strong>Breadcrumb:</strong><br>';
        woocommerce_breadcrumb();
        echo '</div>';
    } else {
        echo '<p style="color: red;">woocommerce_breadcrumb fonksiyonu bulunamadi!</p>';
    }
    
    wp_reset_postdata();
} else {
    echo '<p style="color: red;">Hic urun bulunamadi!</p>';
}

echo '<p><a href="javascript:history.back()">Geri Don</a></p>';
?>
